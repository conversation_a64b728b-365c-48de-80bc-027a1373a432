<?php

use App\Http\Controllers\ProfileController;
use App\Http\Middleware\RoleMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

// Public Landing Pages
Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/services-info', function () {
    return Inertia::render('landing/services-info');
})->name('services-info');

Route::get('/faqs', function () {
    return Inertia::render('landing/faqs');
})->name('faqs');

Route::get('/contact', function () {
    return Inertia::render('landing/contact');
})->name('contact');

// Applicant Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::middleware(RoleMiddleware::class . ':applicant')->group(function () {
        Route::get('/applicant/dashboard', function () {
            return Inertia::render('applicant/dashboard');
        })->name('applicant.dashboard');

        Route::get('/applicant/residency-verification', function () {
            return Inertia::render('applicant/residency-verification');
        })->name('applicant.residency-verification');

        Route::get('/applicant/bfaces-application', function () {
            return Inertia::render('applicant/bfaces-application');
        })->name('applicant.bfaces-application');

        Route::get('/applicant/services', function () {
            return Inertia::render('applicant/services');
        })->name('applicant.services');

        Route::get('/applicant/applications', function () {
            return Inertia::render('applicant/applications');
        })->name('applicant.applications');

        Route::get('/applicant/appointments', function () {
            return Inertia::render('applicant/appointments');
        })->name('applicant.appointments');

        Route::get('/applicant/documents', function () {
            return Inertia::render('applicant/documents');
        })->name('applicant.documents');

        // Applicant Settings Routes
        Route::prefix('applicant/settings')->group(function () {
            Route::get('/profile', function () {
                $user = auth()->user();
                $profile = $user->profile;

                return Inertia::render('applicant/settings/profile', [
                    'user' => [
                        'name' => $profile ? $profile->getFullNameAttribute() : $user->name,
                        'email' => $user->email,
                        'phone' => $profile ? $profile->contact_number : null,
                        'barangay' => $profile ? $profile->barangay : null,
                        'address' => $profile ? $profile->getFullAddressAttribute() : null,
                    ],
                ]);
            })->name('applicant.settings.profile');

            Route::patch('/profile', function (Request $request) {
                $user = auth()->user();
                $profile = $user->profile ?? $user->profile()->create([]);

                $validated = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
                    'phone' => ['nullable', 'string', 'max:255'],
                    'barangay' => ['nullable', 'string', 'max:255'],
                    'address' => ['nullable', 'string', 'max:255'],
                ]);

                // Update user email if changed
                if ($validated['email'] !== $user->email) {
                    $user->email = $validated['email'];
                    $user->email_verified_at = null;
                    $user->save();
                }

                // Update profile
                $names = explode(' ', $validated['name']);
                $profile->update([
                    'first_name' => $names[0],
                    'last_name' => end($names),
                    'middle_name' => count($names) > 2 ? implode(' ', array_slice($names, 1, -1)) : null,
                    'contact_number' => $validated['phone'],
                    'barangay' => $validated['barangay'],
                    'street_address' => $validated['address'],
                ]);

                return back();
            })->name('applicant.settings.profile.update');

            Route::get('/password', function () {
                return Inertia::render('applicant/settings/password');
            })->name('applicant.settings.password');

            Route::put('/password', function (Request $request) {
                $validated = $request->validate([
                    'current_password' => ['required', 'current_password'],
                    'password' => ['required', 'confirmed', 'min:8'],
                ]);

                $request->user()->update([
                    'password' => bcrypt($validated['password']),
                ]);

                return back();
            })->name('applicant.settings.password.update');
        });
    });

    // Social Worker Routes
    Route::middleware(RoleMiddleware::class . ':social-worker')->prefix('social-worker')->group(function () {
        Route::get('/dashboard', function () {
            return Inertia::render('social-worker/dashboard');
        })->name('social-worker.dashboard');

        Route::get('/verifications', function () {
            return Inertia::render('social-worker/verifications');
        })->name('social-worker.verifications');

        Route::get('/applications', function () {
            return Inertia::render('social-worker/applications');
        })->name('social-worker.applications');

        Route::get('/interviews', function () {
            return Inertia::render('social-worker/interviews');
        })->name('social-worker.interviews');

        Route::get('/reports', function () {
            return Inertia::render('social-worker/reports');
        })->name('social-worker.reports');

        Route::get('/clients', function () {
            return Inertia::render('social-worker/clients');
        })->name('social-worker.clients');

        Route::get('/clients/{id}', function (string $id) {
            return Inertia::render('social-worker/clients/[id]', [
                'params' => [
                    'id' => $id
                ]
            ]);
        })->name('social-worker.clients.show');
        
        // Social Worker Settings Routes
        Route::prefix('settings')->group(function () {
            Route::get('/profile', function () {
                $user = auth()->user();

                return Inertia::render('social-worker/settings/profile', [
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone ?? null,
                    ],
                ]);
            })->name('social-worker.settings.profile');

            Route::patch('/profile', function (Request $request) {
                $user = auth()->user();

                $validated = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
                    'phone' => ['nullable', 'string', 'max:255'],
                ]);

                // Update user
                $user->name = $validated['name'];
                if ($validated['email'] !== $user->email) {
                    $user->email = $validated['email'];
                    $user->email_verified_at = null;
                }
                $user->phone = $validated['phone'] ?? null;
                $user->save();

                return back();
            })->name('social-worker.settings.profile.update');

            Route::get('/password', function () {
                return Inertia::render('social-worker/settings/password');
            })->name('social-worker.settings.password');

            Route::put('/password', function (Request $request) {
                $validated = $request->validate([
                    'current_password' => ['required', 'current_password'],
                    'password' => ['required', 'confirmed', 'min:8'],
                ]);

                $request->user()->update([
                    'password' => bcrypt($validated['password']),
                ]);

                return back();
            })->name('social-worker.settings.password.update');

            Route::get('/appearance', function () {
                return Inertia::render('social-worker/settings/appearance');
            })->name('social-worker.settings.appearance');
        });
    });

    // MSWDO Officer Routes
    Route::middleware(RoleMiddleware::class . ':mswdo-officer')->prefix('mswdo-officer')->group(function () {
        Route::get('/dashboard', function () {
            return Inertia::render('mswdo-officer/dashboard');
        })->name('mswdo-officer.dashboard');

        Route::get('/configuration', function () {
            return Inertia::render('mswdo-officer/configuration');
        })->name('mswdo-officer.configuration');
        
        // Services Management Routes
        Route::prefix('services')->group(function () {
            Route::get('/', function () {
                return Inertia::render('mswdo-officer/services', [
                    'categories' => [],
                    'services' => [],
                    'totalServices' => 0,
                    'activeServices' => 0,
                    'totalCategories' => 0,
                    'activeCategories' => 0,
                ]);
            })->name('mswdo-officer.services');

            Route::get('/new', function () {
                return Inertia::render('mswdo-officer/services/[id]', [
                    'categories' => [], // Add categories data here
                ]);
            })->name('mswdo-officer.services.create');

            Route::get('/{id}/edit', function (string $id) {
                return Inertia::render('mswdo-officer/services/[id]', [
                    'categories' => [], // Add categories data here
                    'service' => [], // Add service data here
                ]);
            })->name('mswdo-officer.services.edit');

            // Service Categories Routes
            Route::prefix('categories')->group(function () {
                Route::get('/new', function () {
                    return Inertia::render('mswdo-officer/services/categories/[id]');
                })->name('mswdo-officer.services.categories.create');

                Route::get('/{id}/edit', function (string $id) {
                    return Inertia::render('mswdo-officer/services/categories/[id]', [
                        'category' => [], // Add category data here
                    ]);
                })->name('mswdo-officer.services.categories.edit');
            });

            // Service Requirements Routes
            Route::prefix('requirements')->group(function () {
                Route::get('/new', function () {
                    return Inertia::render('mswdo-officer/services/requirements/[id]', [
                        'services' => [], // Add services data here
                    ]);
                })->name('mswdo-officer.services.requirements.create');

                Route::get('/{id}/edit', function (string $id) {
                    return Inertia::render('mswdo-officer/services/requirements/[id]', [
                        'services' => [], // Add services data here
                        'requirement' => [], // Add requirement data here
                    ]);
                })->name('mswdo-officer.services.requirements.edit');
            });
        });
        
        Route::get('/users', function () {
            return Inertia::render('mswdo-officer/users');
        })->name('mswdo-officer.users');

        Route::get('/budget', function () {
            return Inertia::render('mswdo-officer/budget');
        })->name('mswdo-officer.budget');

        Route::get('/reports', function () {
            return Inertia::render('mswdo-officer/reports');
        })->name('mswdo-officer.reports');

        Route::get('/database', function () {
            return Inertia::render('mswdo-officer/database');
        })->name('mswdo-officer.database');
        
        // MSWDO Officer Settings Routes
        Route::prefix('settings')->group(function () {
            Route::get('/profile', function () {
                $user = auth()->user();

                return Inertia::render('mswdo-officer/settings/profile', [
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone ?? null,
                    ],
                ]);
            })->name('mswdo-officer.settings.profile');

            Route::patch('/profile', function (Request $request) {
                $user = auth()->user();

                $validated = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
                    'phone' => ['nullable', 'string', 'max:255'],
                ]);

                // Update user
                $user->name = $validated['name'];
                if ($validated['email'] !== $user->email) {
                    $user->email = $validated['email'];
                    $user->email_verified_at = null;
                }
                $user->phone = $validated['phone'] ?? null;
                $user->save();

                return back();
            })->name('mswdo-officer.settings.profile.update');

            Route::get('/password', function () {
                return Inertia::render('mswdo-officer/settings/password');
            })->name('mswdo-officer.settings.password');

            Route::put('/password', function (Request $request) {
                $validated = $request->validate([
                    'current_password' => ['required', 'current_password'],
                    'password' => ['required', 'confirmed', 'min:8'],
                ]);

                $request->user()->update([
                    'password' => bcrypt($validated['password']),
                ]);

                return back();
            })->name('mswdo-officer.settings.password.update');

            Route::get('/appearance', function () {
                return Inertia::render('mswdo-officer/settings/appearance');
            })->name('mswdo-officer.settings.appearance');
        });

        // User Management Routes
        Route::get('/users', function () {
            return Inertia::render('mswdo-officer/users');
        })->name('mswdo-officer.users');

        // Social Worker Management Routes
        Route::get('/users/social-workers/new', function () {
            return Inertia::render('mswdo-officer/users/social-workers/new');
        })->name('mswdo-officer.users.social-workers.create');

        Route::get('/users/social-workers/{id}', function (string $id) {
            return Inertia::render('mswdo-officer/users/social-workers/[id]', [
                'id' => $id
            ]);
        })->name('mswdo-officer.users.social-workers.edit');

        // Applicant Management Routes
        Route::get('/users/applicants/new', function () {
            return Inertia::render('mswdo-officer/users/applicants/new');
        })->name('mswdo-officer.users.applicants.create');

        Route::get('/users/applicants/{id}', function (string $id) {
            return Inertia::render('mswdo-officer/users/applicants/[id]', [
                'id' => $id
            ]);
        })->name('mswdo-officer.users.applicants.edit');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
