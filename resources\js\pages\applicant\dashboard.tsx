import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app/app-sidebar-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { CheckCircle, Clock, HelpCircle, AlertCircle, Calendar, ClipboardList, FileText, Files } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Helper function to get status icon
const getStatusIcon = (status: string) => {
    switch (status) {
        case 'verified':
            return <CheckCircle className="h-5 w-5 text-green-500" />;
        case 'pending':
        case 'submitted':
        case 'under_review':
            return <Clock className="h-5 w-5 text-amber-500" />;
        case 'rejected':
            return <AlertCircle className="h-5 w-5 text-red-500" />;
        case 'unfilled':
            return <HelpCircle className="h-5 w-5 text-gray-500" />;
        default:
            return <HelpCircle className="h-5 w-5 text-gray-500" />;
    }
};

// Helper function to get status text
const getStatusText = (status: string) => {
    return status.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
};

export default function ClientDashboard() {
    // This would come from your backend
    const userData = {
        name: 'Juan Dela Cruz',
        residencyStatus: 'verified',
        bfacesStatus: 'verified',
        activeApplications: 2,
        upcomingAppointments: 1,
        completedServices: 3,
        cooldownUntil: '2024-08-15',
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Client Dashboard" />
            
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight">Welcome, {userData.name}</h1>
                    <Button className="bg-purple-600 hover:bg-purple-700" asChild>
                        <a href="/services">Apply for Service</a>
                    </Button>
                </div>

                {/* Status Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Residency Status</CardTitle>
                            {getStatusIcon(userData.residencyStatus)}
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl font-bold">{getStatusText(userData.residencyStatus)}</div>
                            <CardDescription>
                                {userData.residencyStatus === 'verified' 
                                    ? 'Your residency in Balagtas has been verified.' 
                                    : 'Your residency verification is being processed.'}
                            </CardDescription>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">BFACES Status</CardTitle>
                            {getStatusIcon(userData.bfacesStatus)}
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl font-bold">{getStatusText(userData.bfacesStatus)}</div>
                            <CardDescription>
                                {userData.bfacesStatus === 'verified' 
                                    ? 'Your BFACES application has been approved.' 
                                    : 'Complete your BFACES application to access services.'}
                            </CardDescription>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Service Cooldown</CardTitle>
                            <Clock className="h-5 w-5 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl font-bold">Until {new Date(userData.cooldownUntil).toLocaleDateString()}</div>
                            <CardDescription>
                                Next eligible date for new applications
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Activity Overview */}
                <h2 className="mt-4 text-xl font-semibold">Activity Overview</h2>
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Active Applications</CardTitle>
                            <FileText className="h-4 w-4 text-purple-400" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userData.activeApplications}</div>
                            <p className="text-xs text-muted-foreground">+1 since last month</p>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Upcoming Appointments</CardTitle>
                            <Calendar className="h-4 w-4 text-purple-400" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userData.upcomingAppointments}</div>
                            <p className="text-xs text-muted-foreground">Next: Tomorrow, 2:00 PM</p>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                            <CardTitle className="text-sm font-medium">Completed Services</CardTitle>
                            <CheckCircle className="h-5 w-5 text-green-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl font-bold">{userData.completedServices}</div>
                            <CardDescription>
                                Successfully received services
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <h2 className="mt-4 text-xl font-semibold">Recent Activity</h2>
                <Card>
                    <CardContent className="p-0">
                        <div className="divide-y divide-border rounded-md border">
                            <div className="flex items-center justify-between p-4">
                                <div className="space-y-1">
                                    <p className="text-sm font-medium leading-none">Medical Assistance Application Submitted</p>
                                    <p className="text-sm text-muted-foreground">Your application is under review.</p>
                                </div>
                                <div className="text-sm text-muted-foreground">2 days ago</div>
                            </div>
                            <div className="flex items-center justify-between p-4">
                                <div className="space-y-1">
                                    <p className="text-sm font-medium leading-none">Appointment Scheduled</p>
                                    <p className="text-sm text-muted-foreground">Social worker interview on April 15, 2024</p>
                                </div>
                                <div className="text-sm text-muted-foreground">3 days ago</div>
                            </div>
                            <div className="flex items-center justify-between p-4">
                                <div className="space-y-1">
                                    <p className="text-sm font-medium leading-none">Document Verification Complete</p>
                                    <p className="text-sm text-muted-foreground">All submitted documents have been verified.</p>
                                </div>
                                <div className="text-sm text-muted-foreground">1 week ago</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Actions 
                <h2 className="mt-4 text-xl font-semibold">Quick Actions</h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <a href="/services" className="block">
                        <Card className="cursor-pointer hover:bg-muted/50 hover:scale-105 hover:shadow-md transition-all duration-200">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <FileText className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">New Application</h3>
                            </CardContent>
                        </Card>
                    </a>
                    <a href="/appointments" className="block">
                        <Card className="cursor-pointer hover:bg-muted/50 hover:scale-105 hover:shadow-md transition-all duration-200">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <Calendar className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">Schedule Appointment</h3>
                            </CardContent>
                        </Card>
                    </a>
                    <a href="/applications" className="block">
                        <Card className="cursor-pointer hover:bg-muted/50 hover:scale-105 hover:shadow-md transition-all duration-200">
                            <CardContent className="flex flex-col items-center justify-center p-6">
                                <ClipboardList className="h-8 w-8 text-muted-foreground mb-2" />
                                <h3 className="font-medium">Track Applications</h3>
                            </CardContent>
                        </Card>
                    </a>
                </div>
                */}
            </div>
        </AppLayout>
    );
} 