import React, { useState, useEffect } from 'react';
import { Head, usePage, useForm } from '@inertiajs/react';
import { type SharedData } from '@/types';
import LandingLayout from '@/components/landing-layout';
import LandingNav from '@/components/landing-nav';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
    MapPinIcon, 
    PhoneIcon, 
    MailIcon, 
    ClockIcon,
    FacebookIcon,
    InstagramIcon,
    TwitterIcon,
    FileTextIcon,
    HeartIcon,
    HandHeartIcon,
    ShieldIcon,
    AlertCircleIcon,
    UserCheckIcon,
    MessageSquareIcon
} from 'lucide-react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

// Skeleton components
const ContactFormSkeleton = () => (
    <div className="space-y-4">
        <Skeleton height={40} className="mb-4" />
        <Skeleton height={40} className="mb-4" />
        <Skeleton height={40} className="mb-4" />
        <Skeleton height={100} className="mb-4" />
        <div className="flex gap-4">
            <Skeleton circle width={20} height={20} />
            <Skeleton width={100} height={20} />
        </div>
        <Skeleton height={40} width={120} />
    </div>
);

const ContactCardSkeleton = () => (
    <Card>
        <CardHeader>
            <Skeleton width={200} height={24} className="mb-2" />
        </CardHeader>
        <CardContent>
            <div className="space-y-4">
                <div className="flex items-center gap-3">
                    <Skeleton circle width={32} height={32} />
                    <Skeleton width={150} />
                </div>
                <div className="flex items-center gap-3">
                    <Skeleton circle width={32} height={32} />
                    <Skeleton width={200} />
                </div>
            </div>
        </CardContent>
    </Card>
);

const MapSectionSkeleton = () => (
    <div className="grid md:grid-cols-2 gap-8">
        <div>
            <Skeleton height={300} className="rounded-lg" />
        </div>
        <div className="space-y-6">
            <Skeleton width={200} height={32} className="mb-4" />
            <div className="space-y-4">
                <div className="flex items-start gap-3">
                    <Skeleton circle width={24} height={24} className="shrink-0" />
                    <div>
                        <Skeleton width={200} className="mb-2" />
                        <Skeleton count={2} />
                    </div>
                </div>
                <div className="flex items-start gap-3">
                    <Skeleton circle width={24} height={24} className="shrink-0" />
                    <div>
                        <Skeleton width={200} className="mb-2" />
                        <Skeleton count={2} />
                    </div>
                </div>
            </div>
        </div>
    </div>
);

type ContactForm = {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    inquiryType: string;
    hasAccount: string;
    message: string;
};

export default function Contact() {
    const { auth } = usePage<SharedData>().props;
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm<ContactForm>({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        inquiryType: '',
        hasAccount: 'no',
        message: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // For now, just simulate form submission since backend isn't implemented
        setIsSubmitted(true);
        reset();

        // Reset success message after 5 seconds
        setTimeout(() => {
            setIsSubmitted(false);
        }, 5000);

        // TODO: When backend is ready, use:
        // post(route('contact.store'), {
        //     onSuccess: () => {
        //         setIsSubmitted(true);
        //         reset();
        //     }
        // });
    };

    return (
        <LandingLayout title="Contact Us">
            <Head title="Contact - Balagtas Social Care" />
            
            <div className="container mx-auto px-4 py-12">
                {/* Introduction */}
                <div className="max-w-3xl mx-auto mb-16 text-center">
                    {isLoading ? (
                        <>
                            <Skeleton height={40} width={300} className="mb-4" />
                            <Skeleton count={2} />
                        </>
                    ) : (
                        <>
                            <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                                <PhoneIcon className="h-4 w-4" />
                                Contact & Support
                            </div>
                            <h1 className="text-4xl md:text-5xl font-bold text-purple-900 mb-6 leading-tight">Get in Touch</h1>
                            <p className="text-xl text-gray-600 leading-relaxed mb-8">
                                We're here to help you at every step of your social care journey -
                                from residency verification to service availment. Reach out to us
                                using any of the contact methods below.
                            </p>
                            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-500">
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span>24/7 Support</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span>Multiple Channels</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                    <span>Expert Assistance</span>
                                </div>
                            </div>
                        </>
                    )}
                </div>

                {/* Main Contact Section */}
                <div className="bg-white rounded-xl shadow-sm border border-purple-100 p-8 mb-20">
                    <div className="grid md:grid-cols-2 gap-12">
                        {/* Contact Form */}
                        <div>
                            <h2 className="text-2xl font-bold text-purple-900 mb-6">Send Us a Message</h2>
                            
                            {isLoading ? (
                                <ContactFormSkeleton />
                            ) : (
                                <>
                                    {isSubmitted && (
                                        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                            <p className="text-green-800 font-medium">Thank you for your message!</p>
                                            <p className="text-green-600 text-sm mt-1">
                                                We've received your inquiry and will respond within 24 hours.
                                            </p>
                                        </div>
                                    )}

                                    <form className="space-y-6" onSubmit={handleSubmit}>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="firstName">First Name</Label>
                                                <Input
                                                    id="firstName"
                                                    placeholder="Your first name"
                                                    value={data.firstName}
                                                    onChange={(e) => setData('firstName', e.target.value)}
                                                    required
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="lastName">Last Name</Label>
                                                <Input
                                                    id="lastName"
                                                    placeholder="Your last name"
                                                    value={data.lastName}
                                                    onChange={(e) => setData('lastName', e.target.value)}
                                                    required
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email">Email Address</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                placeholder="Your email address"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                required
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Phone Number</Label>
                                            <Input
                                                id="phone"
                                                placeholder="Your phone number"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="inquiryType">Inquiry Type</Label>
                                            <Select value={data.inquiryType} onValueChange={(value) => setData('inquiryType', value)}>
                                                <SelectTrigger id="inquiryType">
                                                    <SelectValue placeholder="Select inquiry type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="account">Account & Registration</SelectItem>
                                                    <SelectItem value="residency">Residency Verification</SelectItem>
                                                    <SelectItem value="bfaces">BFACES Application</SelectItem>
                                                    <SelectItem value="medical">Medical Assistance</SelectItem>
                                                    <SelectItem value="burial">Burial Assistance</SelectItem>
                                                    <SelectItem value="senior">Aid to Senior Citizens</SelectItem>
                                                    <SelectItem value="pao">PAO Certification</SelectItem>
                                                    <SelectItem value="philhealth">PhilHealth Certification</SelectItem>
                                                    <SelectItem value="other">Other Inquiry</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label>Do you have an existing account?</Label>
                                            <div className="flex space-x-4">
                                                <div className="flex items-center space-x-2">
                                                    <input
                                                        type="radio"
                                                        id="yes"
                                                        name="hasAccount"
                                                        value="yes"
                                                        checked={data.hasAccount === 'yes'}
                                                        onChange={(e) => setData('hasAccount', e.target.value)}
                                                        className="h-4 w-4 text-purple-600 focus:ring-purple-500"
                                                    />
                                                    <Label htmlFor="yes">Yes</Label>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <input
                                                        type="radio"
                                                        id="no"
                                                        name="hasAccount"
                                                        value="no"
                                                        checked={data.hasAccount === 'no'}
                                                        onChange={(e) => setData('hasAccount', e.target.value)}
                                                        className="h-4 w-4 text-purple-600 focus:ring-purple-500"
                                                    />
                                                    <Label htmlFor="no">No</Label>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="message">Message</Label>
                                            <Textarea
                                                id="message"
                                                placeholder="Please provide details about your inquiry"
                                                rows={6}
                                                value={data.message}
                                                onChange={(e) => setData('message', e.target.value)}
                                                required
                                            />
                                        </div>

                                        <Button
                                            type="submit"
                                            className="w-full bg-purple-600 hover:bg-purple-700"
                                            disabled={processing}
                                        >
                                            {processing ? 'Sending...' : 'Send Message'}
                                        </Button>

                                        <p className="text-sm text-gray-500 text-center">
                                            By submitting this form, you agree to our privacy policy and terms of service.
                                        </p>
                                    </form>
                                </>
                            )}
                        </div>
                        
                        {/* Contact Information */}
                        <div>
                            <h2 className="text-2xl font-bold text-purple-900 mb-6">Contact Information</h2>
                            
                            <div className="bg-purple-50 rounded-lg p-6 mb-8">
                                <div className="space-y-6">
                                    <div className="flex items-start gap-4">
                                        <div className="bg-purple-100 p-2 rounded-full shrink-0">
                                            <MapPinIcon className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-1">Office Address</h3>
                                            <p className="text-gray-600">
                                                Municipal Social Welfare and Development Office<br />
                                                2nd Floor, Balagtas Municipal Hall<br />
                                                Balagtas, Bulacan, Philippines
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-start gap-4">
                                        <div className="bg-purple-100 p-2 rounded-full shrink-0">
                                            <PhoneIcon className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-1">Phone Numbers</h3>
                                            <p className="text-gray-600">
                                                Office: (*************<br />
                                                Emergency Hotline: (*************<br />
                                                Mobile: +63 ************
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-start gap-4">
                                        <div className="bg-purple-100 p-2 rounded-full shrink-0">
                                            <MailIcon className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-1">Email Addresses</h3>
                                            <p className="text-gray-600">
                                                General Inquiries: <EMAIL><br />
                                                Support: <EMAIL><br />
                                                Applications: <EMAIL>
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-start gap-4">
                                        <div className="bg-purple-100 p-2 rounded-full shrink-0">
                                            <ClockIcon className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 mb-1">Office Hours</h3>
                                            <p className="text-gray-600">
                                                Monday - Friday: 8:00 AM - 5:00 PM<br />
                                                Saturday: 8:00 AM - 12:00 PM<br />
                                                Sunday: Closed<br />
                                                <span className="text-purple-600 font-medium">24/7 Emergency Hotline Available</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="space-y-6">
                                <div>
                                    <h3 className="font-semibold text-gray-900 mb-4">Follow Us</h3>
                                    <div className="flex gap-3">
                                        <a href="#" className="bg-purple-100 p-3 rounded-full hover:bg-purple-200 transition-colors">
                                            <FacebookIcon className="h-5 w-5 text-purple-600" />
                                        </a>
                                        <a href="#" className="bg-purple-100 p-3 rounded-full hover:bg-purple-200 transition-colors">
                                            <InstagramIcon className="h-5 w-5 text-purple-600" />
                                        </a>
                                        <a href="#" className="bg-purple-100 p-3 rounded-full hover:bg-purple-200 transition-colors">
                                            <TwitterIcon className="h-5 w-5 text-purple-600" />
                                        </a>
                                    </div>
                                </div>
                                
                                <Separator className="my-8 bg-purple-100" />
                                
                                <div>
                                    <h3 className="font-semibold text-gray-900 mb-4">Service Hours</h3>
                                    <div className="space-y-2 bg-white p-4 rounded-lg border border-purple-100">
                                        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                                            <span className="text-gray-600">Document Processing</span>
                                            <span className="text-gray-900">8:30 AM - 4:00 PM</span>
                                        </div>
                                        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                                            <span className="text-gray-600">Application Filing</span>
                                            <span className="text-gray-900">8:30 AM - 3:00 PM</span>
                                        </div>
                                        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                                            <span className="text-gray-600">Counseling Services</span>
                                            <span className="text-gray-900">9:00 AM - 4:00 PM</span>
                                        </div>
                                        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                                            <span className="text-gray-600">Aid Distribution</span>
                                            <span className="text-gray-900">10:00 AM - 3:00 PM</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-600">Emergency Assistance</span>
                                            <span className="text-gray-900">24/7</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Process-Specific Contact Cards */}
                <div className="mb-20">
                    <div className="text-center mb-12">
                        <h2 className="text-2xl font-bold text-purple-900 mb-3">Contact Us By Process</h2>
                        <p className="text-gray-600 max-w-3xl mx-auto">
                            Each stage of the Balagtas Social Care process has dedicated staff who can assist you. 
                            Find the appropriate contact information for your specific needs below.
                        </p>
                    </div>
                    
                    <div className="grid md:grid-cols-3 gap-6">
                        <Card className="border-purple-100 hover:shadow-lg transition-all duration-300 overflow-hidden group">
                            <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 pb-4 pt-6 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                <div className="flex items-start gap-4">
                                    <div className="bg-purple-600 p-3 rounded-xl shadow-md group-hover:bg-purple-700 transition-colors duration-300">
                                        <UserCheckIcon className="h-6 w-6 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-lg font-bold text-purple-900 leading-tight mb-1">
                                            Residency Verification
                                        </CardTitle>
                                        <div className="h-1 w-12 bg-purple-600 rounded-full"></div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                                <p className="text-gray-600 text-sm mb-4">
                                    For assistance with account registration and residency verification
                                </p>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center gap-2">
                                        <PhoneIcon className="h-4 w-4 text-purple-600" />
                                        <span>(************* ext. 201</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MailIcon className="h-4 w-4 text-purple-600" />
                                        <span><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MessageSquareIcon className="h-4 w-4 text-purple-600" />
                                        <span>Chat with Verification Team</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card className="border-purple-100 hover:shadow-lg transition-all duration-300 overflow-hidden group">
                            <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 pb-4 pt-6 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                <div className="flex items-start gap-4">
                                    <div className="bg-purple-600 p-3 rounded-xl shadow-md group-hover:bg-purple-700 transition-colors duration-300">
                                        <FileTextIcon className="h-6 w-6 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-lg font-bold text-purple-900 leading-tight mb-1">
                                            BFACES Application
                                        </CardTitle>
                                        <div className="h-1 w-12 bg-purple-600 rounded-full"></div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                                <p className="text-gray-600 text-sm mb-4">
                                    For help with your BFACES application and verification
                                </p>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center gap-2">
                                        <PhoneIcon className="h-4 w-4 text-purple-600" />
                                        <span>(************* ext. 202</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MailIcon className="h-4 w-4 text-purple-600" />
                                        <span><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MessageSquareIcon className="h-4 w-4 text-purple-600" />
                                        <span>Chat with BFACES Team</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card className="border-purple-100 hover:shadow-lg transition-all duration-300 overflow-hidden group">
                            <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 pb-4 pt-6 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                <div className="flex items-start gap-4">
                                    <div className="bg-purple-600 p-3 rounded-xl shadow-md group-hover:bg-purple-700 transition-colors duration-300">
                                        <HeartIcon className="h-6 w-6 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-lg font-bold text-purple-900 leading-tight mb-1">
                                            Service Applications
                                        </CardTitle>
                                        <div className="h-1 w-12 bg-purple-600 rounded-full"></div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                                <p className="text-gray-600 text-sm mb-4">
                                    For inquiries about specific services and application status
                                </p>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center gap-2">
                                        <PhoneIcon className="h-4 w-4 text-purple-600" />
                                        <span>(************* ext. 203</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MailIcon className="h-4 w-4 text-purple-600" />
                                        <span><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MessageSquareIcon className="h-4 w-4 text-purple-600" />
                                        <span>Chat with Services Team</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card className="border-purple-100 hover:shadow-lg transition-all duration-300 overflow-hidden group">
                            <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 pb-4 pt-6 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                <div className="flex items-start gap-4">
                                    <div className="bg-purple-600 p-3 rounded-xl shadow-md group-hover:bg-purple-700 transition-colors duration-300">
                                        <ShieldIcon className="h-6 w-6 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-lg font-bold text-purple-900 leading-tight mb-1">
                                            Senior Citizen Services
                                        </CardTitle>
                                        <div className="h-1 w-12 bg-purple-600 rounded-full"></div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                                <p className="text-gray-600 text-sm mb-4">
                                    Dedicated support for elderly residents requiring assistance
                                </p>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center gap-2">
                                        <PhoneIcon className="h-4 w-4 text-purple-600" />
                                        <span>(************* ext. 204</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MailIcon className="h-4 w-4 text-purple-600" />
                                        <span><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MessageSquareIcon className="h-4 w-4 text-purple-600" />
                                        <span>Chat with Senior Care Team</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card className="border-purple-100 hover:shadow-lg transition-all duration-300 overflow-hidden group">
                            <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 pb-4 pt-6 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                <div className="flex items-start gap-4">
                                    <div className="bg-purple-600 p-3 rounded-xl shadow-md group-hover:bg-purple-700 transition-colors duration-300">
                                        <HandHeartIcon className="h-6 w-6 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-lg font-bold text-purple-900 leading-tight mb-1">
                                            Medical & Burial
                                        </CardTitle>
                                        <div className="h-1 w-12 bg-purple-600 rounded-full"></div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                                <p className="text-gray-600 text-sm mb-4">
                                    For urgent assistance with medical and burial needs
                                </p>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center gap-2">
                                        <PhoneIcon className="h-4 w-4 text-purple-600" />
                                        <span>(************* ext. 205</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MailIcon className="h-4 w-4 text-purple-600" />
                                        <span><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MessageSquareIcon className="h-4 w-4 text-purple-600" />
                                        <span>Chat with Emergency Team</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card className="border-purple-100 hover:shadow-lg transition-all duration-300 overflow-hidden group">
                            <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 pb-4 pt-6 group-hover:from-purple-100 group-hover:to-purple-200 transition-all duration-300">
                                <div className="flex items-start gap-4">
                                    <div className="bg-purple-600 p-3 rounded-xl shadow-md group-hover:bg-purple-700 transition-colors duration-300">
                                        <AlertCircleIcon className="h-6 w-6 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-lg font-bold text-purple-900 leading-tight mb-1">
                                            Technical Support
                                        </CardTitle>
                                        <div className="h-1 w-12 bg-purple-600 rounded-full"></div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                                <p className="text-gray-600 text-sm mb-4">
                                    For website issues, account access, or technical problems
                                </p>
                                <div className="space-y-2 text-sm">
                                    <div className="flex items-center gap-2">
                                        <PhoneIcon className="h-4 w-4 text-purple-600" />
                                        <span>(************* ext. 206</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MailIcon className="h-4 w-4 text-purple-600" />
                                        <span><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MessageSquareIcon className="h-4 w-4 text-purple-600" />
                                        <span>Chat with Tech Support</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
                
                {/* Map Section */}
                <div className="bg-white rounded-xl shadow-sm border border-purple-100 p-8">
                    <h2 className="text-2xl font-bold text-purple-900 mb-6">Find Us on the Map</h2>
                    <div className="bg-gray-200 rounded-lg h-96 flex items-center justify-center">
                        <p className="text-gray-500">
                            Map placeholder - Replace with actual Google Maps or OpenStreetMap embed
                        </p>
                    </div>
                    <div className="mt-6 grid md:grid-cols-2 gap-8">
                        <div className="bg-purple-50 p-6 rounded-lg">
                            <h3 className="font-semibold text-purple-900 mb-3">Directions</h3>
                            <p className="text-gray-600 mb-4">
                                We are conveniently located in the Balagtas Municipal Hall, accessible via public transportation.
                            </p>
                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-1">By Public Transport</h4>
                                    <p className="text-sm text-gray-600">
                                        Take any jeepney or bus headed to Balagtas and alight at Municipal Hall. 
                                        The Social Welfare Office is on the 2nd floor.
                                    </p>
                                </div>
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-1">By Private Vehicle</h4>
                                    <p className="text-sm text-gray-600">
                                        Parking is available in front of the Municipal Hall. 
                                        Please use the designated visitor parking spaces.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-purple-50 p-6 rounded-lg">
                            <h3 className="font-semibold text-purple-900 mb-3">Visiting Hours</h3>
                            <p className="text-gray-600 mb-4">
                                Our office is open to the public during the following hours:
                            </p>
                            <div className="space-y-2">
                                <div className="flex justify-between pb-2 border-b border-purple-100">
                                    <span className="text-gray-600">Monday - Friday</span>
                                    <span className="font-medium">8:00 AM - 5:00 PM</span>
                                </div>
                                <div className="flex justify-between pb-2 border-b border-purple-100">
                                    <span className="text-gray-600">Saturday</span>
                                    <span className="font-medium">8:00 AM - 12:00 PM</span>
                                </div>
                                <div className="flex justify-between pb-2 border-b border-purple-100">
                                    <span className="text-gray-600">Sunday</span>
                                    <span className="font-medium">Closed</span>
                                </div>
                            </div>
                            <p className="mt-4 text-sm text-purple-700 bg-purple-100 p-2 rounded">
                                <strong>Note:</strong> For emergencies outside office hours, please call our 24/7 hotline at (*************.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </LandingLayout>
    );
} 