import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import LandingLayout from '@/components/landing-layout';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { cn } from '@/lib/utils';
import { 
  MegaphoneIcon, 
  HeartHandshakeIcon, 
  UsersIcon, 
  CalendarIcon, 
  ScrollTextIcon,
  ArrowRightIcon,
  UserCheckIcon,
  UserIcon,
  FileCheckIcon,
  ClipboardCheckIcon,
  BellIcon,
  MessageSquareIcon
} from 'lucide-react';

// Skeleton components
const HeroSkeleton = () => (
    <div className="grid md:grid-cols-2 gap-12 items-center">
        <div>
            <Skeleton height={60} className="mb-6" />
            <Skeleton count={3} className="mb-8" />
            <div className="flex gap-4">
                <Skeleton width={120} height={40} />
                <Skeleton width={120} height={40} />
            </div>
        </div>
        <div className="flex justify-center">
            <Skeleton height={400} width="100%" />
        </div>
    </div>
);

const ProcessCardSkeleton = () => (
    <Card className="border-purple-100">
        <CardContent className="p-6">
            <Skeleton circle width={48} height={48} className="mb-4" />
            <Skeleton width={200} height={24} className="mb-2" />
            <Skeleton count={3} className="mb-4" />
            <Skeleton width={150} count={2} />
        </CardContent>
    </Card>
);

const ProcessSectionSkeleton = () => (
    <div className="max-w-5xl mx-auto space-y-16">
        {[1, 2, 3].map((step) => (
            <div key={step}>
                <div className="flex items-center gap-3 mb-6">
                    <Skeleton circle width={40} height={40} />
                    <Skeleton width={200} height={32} />
                </div>
                <div className="ml-13 pl-7 border-l-2 border-purple-200">
                    <div className="grid md:grid-cols-2 gap-8">
                        <ProcessCardSkeleton />
                        <ProcessCardSkeleton />
                    </div>
                </div>
            </div>
        ))}
    </div>
);

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;
    const [isLoading, setIsLoading] = React.useState(false);

    return (
        <LandingLayout>
            {/* Hero Section */}
            <section className="container mx-auto px-4 py-16 md:py-24">
                {isLoading ? (
                    <HeroSkeleton />
                ) : (
                    <div className="grid md:grid-cols-2 gap-12 items-center">
                        <div>
                            <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                                <span className="w-2 h-2 bg-purple-600 rounded-full"></span>
                                Official Government Platform
                            </div>
                            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                                Streamlined Social Services for Balagtas Residents
                            </h1>
                            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                                From residency verification to service availment, we've simplified the process of accessing social care programs through our digital platform.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
                                    <Link href={route('register')} className="flex items-center gap-2">
                                        Get Started Today
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                        </svg>
                                    </Link>
                                </Button>
                                <Button variant="outline" size="lg" className="border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 cursor-pointer">
                                    <Link href={route('services-info')}>
                                        Explore Services
                                    </Link>
                                </Button>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                                <div className="flex items-center gap-1">
                                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <span>Free to use</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <span>24/7 Access</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <span>Secure & Private</span>
                                </div>
                            </div>
                        </div>
                        <div className="flex justify-center">
                            <img 
                                src="/images/hero-image.jpg" 
                                alt="Balagtas Social Care" 
                                className="max-w-full h-auto rounded-xl shadow-lg"
                                onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = 'https://placehold.co/600x400/purple/white?text=Balagtas+Social+Care';
                                }}
                            />
                        </div>
                    </div>
                )}
            </section>

            {/* Process Flow Section */}
            <section className="container mx-auto px-4 py-16 bg-white rounded-t-3xl shadow-inner">
                <div className="text-center mb-16">
                    {isLoading ? (
                        <>
                            <Skeleton height={36} width={300} className="mx-auto mb-4" />
                            <Skeleton height={24} width={500} className="mx-auto" />
                        </>
                    ) : (
                        <>
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Process</h2>
                            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                                Balagtas Social Care follows a layered process to ensure that services are provided efficiently to qualified residents
                            </p>
                        </>
                    )}
                </div>
                
                {isLoading ? (
                    <ProcessSectionSkeleton />
                ) : (
                    <div className="max-w-5xl mx-auto space-y-16">
                        {/* Layer 1 */}
                        <div>
                            <div className="flex items-center gap-3 mb-6">
                                <div className="bg-purple-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold">1</div>
                                <h3 className="text-2xl font-bold text-purple-800">Residency Verification</h3>
                            </div>
                            <div className="ml-13 pl-7 border-l-2 border-purple-200">
                                <div className="grid md:grid-cols-2 gap-8">
                                    <Card className="border-purple-100 hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                                                <UserIcon className="h-6 w-6 text-purple-600" />
                                            </div>
                                            <h4 className="text-xl font-semibold mb-2">Account Registration</h4>
                                            <p className="text-gray-600 mb-4">
                                                Create your account by providing personal details, contact information, and address in Balagtas.
                                            </p>
                                            <ul className="text-sm text-gray-600 space-y-1">
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Complete profile information
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Verify email and mobile number
                                                </li>
                                            </ul>
                                        </CardContent>
                                    </Card>
                                    
                                    <Card className="border-purple-100 hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                                                <UserCheckIcon className="h-6 w-6 text-purple-600" />
                                            </div>
                                            <h4 className="text-xl font-semibold mb-2">Proof of Residency</h4>
                                            <p className="text-gray-600 mb-4">
                                                Upload documents that verify you are a legitimate resident of Balagtas.
                                            </p>
                                            <ul className="text-sm text-gray-600 space-y-1">
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Upload utility bills or government-issued ID
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Manual verification by social worker
                                                </li>
                                            </ul>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                        
                        {/* Layer 2 */}
                        <div>
                            <div className="flex items-center gap-3 mb-6">
                                <div className="bg-purple-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold">2</div>
                                <h3 className="text-2xl font-bold text-purple-800">BFACES Application</h3>
                            </div>
                            <div className="ml-13 pl-7 border-l-2 border-purple-200">
                                <div className="grid md:grid-cols-2 gap-8">
                                    <Card className="border-purple-100 hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                                                <ClipboardCheckIcon className="h-6 w-6 text-purple-600" />
                                            </div>
                                            <h4 className="text-xl font-semibold mb-2">Fill Out BFACES Form</h4>
                                            <p className="text-gray-600 mb-4">
                                                Formal application for emergency/crisis assistance with auto-filled information from your profile.
                                            </p>
                                            <ul className="text-sm text-gray-600 space-y-1">
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Enter household income and details
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Describe emergency/crisis situation
                                                </li>
                                            </ul>
                                        </CardContent>
                                    </Card>
                                    
                                    <Card className="border-purple-100 hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                                                <FileCheckIcon className="h-6 w-6 text-purple-600" />
                                            </div>
                                            <h4 className="text-xl font-semibold mb-2">BFACES Verification</h4>
                                            <p className="text-gray-600 mb-4">
                                                Social worker verification of your BFACES application to unlock service access.
                                            </p>
                                            <ul className="text-sm text-gray-600 space-y-1">
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Application review by social workers
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Unlocks full dashboard and services
                                                </li>
                                            </ul>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                        
                        {/* Layer 3 */}
                        <div>
                            <div className="flex items-center gap-3 mb-6">
                                <div className="bg-purple-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold">3</div>
                                <h3 className="text-2xl font-bold text-purple-800">Services Availment</h3>
                            </div>
                            <div className="ml-13 pl-7">
                                <div className="grid md:grid-cols-2 gap-8">
                                    <Card className="border-purple-100 hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                                                <HeartHandshakeIcon className="h-6 w-6 text-purple-600" />
                                            </div>
                                            <h4 className="text-xl font-semibold mb-2">Select a Service</h4>
                                            <p className="text-gray-600 mb-4">
                                                Browse and apply for specific social welfare services based on your needs. Note that you can only apply for one service at a time.
                                            </p>
                                            <ul className="text-sm text-gray-600 space-y-1">
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Medical Assistance
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Burial Assistance
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Aid to Senior Citizens
                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    and more...
                                </li>
                                            </ul>
                                        </CardContent>
                                    </Card>
                                    
                                    <Card className="border-purple-100 hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                                                <CalendarIcon className="h-6 w-6 text-purple-600" />
                                            </div>
                                            <h4 className="text-xl font-semibold mb-2">Approval Process</h4>
                                            <p className="text-gray-600 mb-4">
                                                Complete verification, interview, and approval steps to receive assistance. Service cooldown rules apply after claiming.
                                            </p>
                                            <ul className="text-sm text-gray-600 space-y-1">
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Document verification
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    On-site interview
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    Final approval within 24 hours
                                                </li>
                                                <li className="flex items-center">
                                                    <span className="bg-purple-600 rounded-full h-1.5 w-1.5 mr-2"></span>
                                                    One service at a time, 12-month cooldown per service
                                </li>
                                            </ul>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </section>

            {/* Statistics Section */}
            <section className="container mx-auto px-4 py-16 bg-purple-50">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">Making a Difference in Balagtas</h2>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Our digital platform has transformed how residents access social welfare services
                    </p>
                </div>

                <div className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                    <div className="text-center">
                        <div className="text-4xl font-bold text-purple-600 mb-2">1,200+</div>
                        <div className="text-gray-600">Families Helped</div>
                    </div>
                    <div className="text-center">
                        <div className="text-4xl font-bold text-purple-600 mb-2">75%</div>
                        <div className="text-gray-600">Faster Processing</div>
                    </div>
                    <div className="text-center">
                        <div className="text-4xl font-bold text-purple-600 mb-2">24/7</div>
                        <div className="text-gray-600">Service Access</div>
                    </div>
                    <div className="text-center">
                        <div className="text-4xl font-bold text-purple-600 mb-2">9</div>
                        <div className="text-gray-600">Barangays Served</div>
                    </div>
                </div>
            </section>

            {/* Testimonials Section */}
            <section className="container mx-auto px-4 py-16">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">Stories from Our Community</h2>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Real experiences from Balagtas residents who have benefited from our services
                    </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                    <Card className="border-purple-100">
                        <CardContent className="p-6">
                            <div className="mb-4">
                                <div className="flex text-yellow-400 mb-2">
                                    {'★'.repeat(5)}
                                </div>
                                <p className="text-gray-600 italic">
                                    "The online application made it so much easier to apply for medical assistance.
                                    I didn't have to take multiple trips to the municipal hall."
                                </p>
                            </div>
                            <div className="flex items-center">
                                <div className="bg-purple-100 rounded-full w-10 h-10 flex items-center justify-center mr-3">
                                    <span className="text-purple-600 font-semibold">MR</span>
                                </div>
                                <div>
                                    <div className="font-semibold">Maria R.</div>
                                    <div className="text-sm text-gray-500">Borol 1st</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-purple-100">
                        <CardContent className="p-6">
                            <div className="mb-4">
                                <div className="flex text-yellow-400 mb-2">
                                    {'★'.repeat(5)}
                                </div>
                                <p className="text-gray-600 italic">
                                    "The social workers were very helpful throughout the BFACES application process.
                                    The system is transparent and I could track my application status."
                                </p>
                            </div>
                            <div className="flex items-center">
                                <div className="bg-purple-100 rounded-full w-10 h-10 flex items-center justify-center mr-3">
                                    <span className="text-purple-600 font-semibold">JS</span>
                                </div>
                                <div>
                                    <div className="font-semibold">Juan S.</div>
                                    <div className="text-sm text-gray-500">Longos</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-purple-100">
                        <CardContent className="p-6">
                            <div className="mb-4">
                                <div className="flex text-yellow-400 mb-2">
                                    {'★'.repeat(5)}
                                </div>
                                <p className="text-gray-600 italic">
                                    "As a senior citizen, I appreciate how easy it is to access services online.
                                    My family can help me apply from home."
                                </p>
                            </div>
                            <div className="flex items-center">
                                <div className="bg-purple-100 rounded-full w-10 h-10 flex items-center justify-center mr-3">
                                    <span className="text-purple-600 font-semibold">LT</span>
                                </div>
                                <div>
                                    <div className="font-semibold">Lola Teresa</div>
                                    <div className="text-sm text-gray-500">Santol</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </section>

            {/* CTA Section */}
            <section className="mx-4 md:mx-auto md:container py-12 md:py-16 bg-purple-600 text-white rounded-lg my-8 md:my-16 max-w-6xl px-6 md:px-8">
                <div className="text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
                    <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
                        Join the Balagtas Social Care system today and access the support you need.
                    </p>
                    <div className="flex flex-wrap justify-center gap-4">
                        <Button variant="outline" size="lg" className="border-white bg-white text-purple-600 hover:bg-purple-600 hover:text-white cursor-pointer">
                            <Link href={route('register')}>
                                Create an Account
                            </Link>
                        </Button>
                        <Button variant="outline" size="lg" className="border-white bg-purple-600 text-white hover:bg-white hover:text-purple-600 cursor-pointer">
                            <Link href={route('login')}>
                                Login
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </LandingLayout>
    );
}
